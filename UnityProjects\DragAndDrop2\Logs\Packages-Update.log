
=== Thu Feb 20 08:14:06 2025

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.modules.ai@1.0.0
  com.unity.modules.androidjni@1.0.0
  com.unity.modules.animation@1.0.0
  com.unity.modules.assetbundle@1.0.0
  com.unity.modules.audio@1.0.0
  com.unity.modules.cloth@1.0.0
  com.unity.modules.director@1.0.0
  com.unity.modules.imageconversion@1.0.0
  com.unity.modules.imgui@1.0.0
  com.unity.modules.jsonserialize@1.0.0
  com.unity.modules.particlesystem@1.0.0
  com.unity.modules.physics@1.0.0
  com.unity.modules.physics2d@1.0.0
  com.unity.modules.screencapture@1.0.0
  com.unity.modules.terrain@1.0.0
  com.unity.modules.terrainphysics@1.0.0
  com.unity.modules.tilemap@1.0.0
  com.unity.modules.ui@1.0.0
  com.unity.modules.uielements@1.0.0
  com.unity.modules.umbra@1.0.0
  com.unity.modules.unityanalytics@1.0.0
  com.unity.modules.unitywebrequest@1.0.0
  com.unity.modules.unitywebrequestassetbundle@1.0.0
  com.unity.modules.unitywebrequestaudio@1.0.0
  com.unity.modules.unitywebrequesttexture@1.0.0
  com.unity.modules.unitywebrequestwww@1.0.0
  com.unity.modules.vehicles@1.0.0
  com.unity.modules.video@1.0.0
  com.unity.modules.vr@1.0.0
  com.unity.modules.wind@1.0.0
  com.unity.modules.xr@1.0.0
  com.unity.modules.accessibility@1.0.0
  com.unity.multiplayer.center@1.0.0
The following packages were updated:
  com.unity.inputsystem from version 1.11.2 to 1.13.0
  com.unity.test-framework from version 1.4.5 to 1.4.6
