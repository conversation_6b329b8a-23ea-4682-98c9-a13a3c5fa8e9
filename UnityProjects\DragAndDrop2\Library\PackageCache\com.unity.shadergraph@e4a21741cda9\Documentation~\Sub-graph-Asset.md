# Subgraph Asset

## Description

The **Subgraph Asset** is a new **Asset** type introduced with the Shader Graph. A **Subgraph Asset** defines a [Subgraph](Sub-graph.md). This is different to a Shader Graph. You can create a **Subgraph Asset** from the [Project window](https://docs.unity3d.com/Manual/ProjectView.html.md) from the **Create** menu via **Subgraph** in the **Shader** sub-menu.

You can open the [Shader Graph Window](Shader-Graph-Window.md) by double clicking a **Subgraph Asset** or by clicking **Open Shader Editor** in the [Inspector](https://docs.unity3d.com/Manual/UsingTheInspector.html) when the **Subgraph Asset** is selected.
