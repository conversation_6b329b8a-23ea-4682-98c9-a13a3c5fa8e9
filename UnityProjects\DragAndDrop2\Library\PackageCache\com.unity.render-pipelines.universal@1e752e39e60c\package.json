{"name": "com.unity.render-pipelines.universal", "description": "The Universal Render Pipeline (URP) is a prebuilt Scriptable Render Pipeline, made by Unity. URP provides artist-friendly workflows that let you quickly and easily create optimized graphics across a range of platforms, from mobile to high-end consoles and PCs.", "version": "17.0.3", "unity": "6000.0", "displayName": "Universal RP", "dependencies": {"com.unity.render-pipelines.core": "17.0.3", "com.unity.shadergraph": "17.0.3", "com.unity.render-pipelines.universal-config": "17.0.3"}, "keywords": ["graphics", "performance", "rendering", "mobile", "render", "pipeline"], "samples": [{"displayName": "URP Package Samples", "description": "Collection of scenes showcasing different features of the Universal Render Pipeline.", "path": "Samples~/URPPackageSamples"}, {"displayName": "URP RenderGraph Samples", "description": "Collection of scripts with some examples of RenderGraph and how it is used within the Universal Render Pipeline.", "path": "Samples~/URPRenderGraphSamples"}], "_fingerprint": "1e752e39e60cbe5784c8648daa8d82258c2e63f6"}