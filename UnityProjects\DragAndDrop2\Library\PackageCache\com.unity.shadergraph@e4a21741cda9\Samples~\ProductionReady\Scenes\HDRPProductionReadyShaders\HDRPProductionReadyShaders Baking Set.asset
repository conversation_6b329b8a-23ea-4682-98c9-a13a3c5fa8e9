%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4881f9a2c4d568047b316028d20a8dca, type: 3}
  m_Name: HDRPProductionReadyShaders Baking Set
  m_EditorClassIdentifier: 
  singleSceneMode: 1
  dialogNoProbeVolumeInSetShown: 1
  settings:
    m_Version: 1
    dilationSettings:
      enableDilation: 1
      dilationDistance: 1
      dilationValidityThreshold: 0.25
      dilationIterations: 1
      squaredDistWeighting: 1
    virtualOffsetSettings:
      useVirtualOffset: 1
      validityThreshold: 0.25
      outOfGeoOffset: 0.01
      searchMultiplier: 0.2
      rayOriginBias: -0.001
      collisionMask:
        serializedVersion: 2
        m_Bits: 4294967291
  m_SceneGUIDs:
  - 222cbbe63659e844187ee4988f93e660
  obsoleteScenesToNotBake: []
  m_LightingScenarios:
  - Default
  cellDescs:
    m_Keys: 
    m_Values: []
  m_SerializedPerSceneCellList: []
  cellSharedDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  scenarios:
    m_Keys: []
    m_Values: []
  cellBricksDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  cellSupportDataAsset:
    m_AssetGUID: 
    m_StreamableAssetPath: 
    m_ElementSize: 0
    m_StreamableCellDescs:
      m_Keys: 
      m_Values: []
    m_Asset: {fileID: 0}
  chunkSizeInBricks: 128
  maxCellPosition: {x: 0, y: 0, z: 0}
  minCellPosition: {x: 0, y: 0, z: 0}
  globalBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  bakedSimplificationLevels: 3
  bakedMinDistanceBetweenProbes: 1
  bakedSkyOcclusionValue: 0
  bakedSkyShadingDirectionValue: 0
  bakedProbeOffset: {x: 0, y: 0, z: 0}
  maxSHChunkCount: -1
  L0ChunkSize: 0
  L1ChunkSize: 0
  L2TextureChunkSize: 0
  sharedValidityMaskChunkSize: 8192
  sharedSkyOcclusionL0L1ChunkSize: 0
  sharedSkyShadingDirectionIndicesChunkSize: 0
  sharedDataChunkSize: 0
  supportPositionChunkSize: 0
  supportValidityChunkSize: 0
  supportTouchupChunkSize: 0
  supportOffsetsChunkSize: 0
  supportDataChunkSize: 0
  lightingScenario: Default
  version: 1
  freezePlacement: 0
  probeOffset: {x: 0, y: 0, z: 0}
  simplificationLevels: 3
  minDistanceBetweenProbes: 1
  renderersLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  minRendererVolumeSize: 0.1
  skyOcclusion: 0
  skyOcclusionBakingSamples: 2048
  skyOcclusionBakingBounces: 2
  skyOcclusionAverageAlbedo: 0.6
  skyOcclusionBackFaceCulling: 0
  skyOcclusionShadingDirection: 0
  m_SceneBakeData:
    m_Keys:
    - 222cbbe63659e844187ee4988f93e660
    m_Values:
    - hasProbeVolume: 0
      bakeScene: 1
      bounds:
        m_Center: {x: 0, y: 0, z: 0}
        m_Extent: {x: 0, y: 0, z: 0}
