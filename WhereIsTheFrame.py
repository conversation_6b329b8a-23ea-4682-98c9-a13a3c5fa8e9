import cv2
import numpy as np
from skimage.metrics import structural_similarity as ssim

def search_image_in_video(video_path, target_image_path, threshold=0.8, scale_factors=[1.0, 0.9, 0.8, 0.7]):
    """
    Analyzes a video frame by frame to search for a specific image with resizing and color consideration.

    Parameters:
        video_path (str): Path to the input video file.
        target_image_path (str): Path to the image to search for.
        threshold (float): Matching threshold (0 to 1). Higher values mean stricter matching.
        scale_factors (list): List of scaling factors to resize the target image.

    Returns:
        list: List of frame numbers where the image was found.
    """
    # Load the target image (template)
    target_image = cv2.imread(target_image_path, cv2.IMREAD_COLOR)
    if target_image is None:
        raise FileNotFoundError(f"Target image not found at {target_image_path}")
    
    # Convert target image to grayscale for structural similarity
    target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)

    # Open the video file
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise FileNotFoundError(f"Video not found at {video_path}")
    cv2.imshow("Target Image", target_image)
    import time
    time.sleep(5)  # Pause to allow the user to see the target image
    frame_count = 0
    found_frames = []

    while True:
        # Read the next frame
        ret, frame = cap.read()
        if not ret:
            break  # End of video

        # Convert the frame to grayscale
        frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        frame_height, frame_width = frame_gray.shape[:2]

        # Loop over different scaled versions of the target image
        for scale in scale_factors:
            resized_target = cv2.resize(target_image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            resized_gray = cv2.resize(target_gray, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            h, w = resized_gray.shape[:2]

            # Ensure the resized template is smaller than the frame
            if h > frame_height or w > frame_width:
                continue  # Skip this scale if the template is too large

            # Perform template matching
            result = cv2.matchTemplate(frame_gray, resized_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # Check if the match exceeds the threshold
            if max_val >= threshold:
                # Verify color similarity using SSIM
                top_left = max_loc
                bottom_right = (top_left[0] + w, top_left[1] + h)
                matched_region = frame[top_left[1]:bottom_right[1], top_left[0]:bottom_right[0]]
                resized_matched_region = cv2.resize(matched_region, (resized_target.shape[1], resized_target.shape[0]))

                # Calculate structural similarity (SSIM) for color verification
                score, _ = ssim(resized_matched_region, resized_target, full=True, multichannel=True)
                if score >= threshold:
                    print(f"Image found in frame {frame_count} at location {top_left}, scale={scale:.2f}")
                    found_frames.append(frame_count)

                    # Draw a rectangle around the matched region
                    cv2.rectangle(frame, top_left, bottom_right, (0, 255, 0), 2)
                    cv2.imshow("Match", frame)
                    cv2.waitKey(1)  # Display the frame with the match

        frame_count += 1

    # Release resources
    cap.release()
    cv2.destroyAllWindows()

    return found_frames


# Example usage
if __name__ == "__main__":
    video_file = "Video.mp4"  # Path to your video file
    image_to_search = "Target.jpg"  # Path to the image you want to search for
    threshold_value = 0.8  # Adjust this value based on how strict you want the match to be

    found_in_frames = search_image_in_video(video_file, image_to_search, threshold=threshold_value)
    print(f"Image was found in the following frames: {found_in_frames}")