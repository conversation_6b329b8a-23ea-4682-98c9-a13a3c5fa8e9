# Shader Graph keyboard shortcuts reference

Shader Graph provides several keyboard shortcuts to help you work more efficiently. This page lists the keyboard shortcuts available in Shader Graph. You can use the keyboard shortcuts to perform many tasks, such as the following:

- Add nodes to the graph.
- Group and ungroup nodes.
- Open node documentation.
- Toggle some UI elements on and off.

You can also use the Shortcuts Manager to customize the keyboard shortcuts to suit your preferences. For more information on how to customize, refer to [Shortcuts Manager](https://docs.unity3d.com/Manual/ShortcutsManager.html).

## Built-in keyboard shortcuts

The following table lists the built-in keyboard shortcuts available in Shader Graph. You can't customize the built-in keyboard shortcuts. 

| Command               | Windows   | macOS    | Description      |
|:----------------------|:----------|:---------|:-----------------|
| Frame All             | A         | A        | Display the entire graph in the [Shader Graph window](Shader-Graph-Window.md). |
| Frame Selection       | F         | F        | Display the selected nodes of the graph in the [Shader Graph window](Shader-Graph-Window.md). |
| Duplicate             | Ctrl + D  | Cmd + D  | Duplicate the selected nodes. |
| Copy                  | Ctrl + C  | Cmd + C  | Copy the selected nodes to the clipboard. |
| Cut                   | Ctrl + X  | Cmd + X  | Remove the selected nodes from the graph and place them in the clipboard. |
| Paste                 | Ctrl + V  | Cmd + V  | Paste the nodes from the clipboard. |
| Undo                  | Ctrl + Z  | Cmd + Z  | Undo the last action. |
| Redo                  | Ctrl + Y  | Cmd + Y  | Redo the last action. |
| Open Create Node menu | Space     | Space    | Open the (Create Node menu)[Create-Node-Menu.md] to add nodes to your graph. |

## Customizable shortcuts

The following table lists the keyboard shortcuts available in Shader Graph that you can customize with [Shortcuts Manager](https://docs.unity3d.com/Manual/ShortcutsManager.html).

| Command               | Windows          | macOS           | Description      |
|:----------------------|:-----------------|:----------------|:-----------------|
| Close Tab             | Ctrl + F4        | Cmd + F4        | Close the selected tab. |
| Cycle Color Mode      | Shift + 4        | Shift + 4       | Cycle through the different color modes. |
| Group Selected Nodes  | Ctrl + G         | Cmd + G         | Group the selected nodes. |
| Insert Redirect Node  | Ctrl + R         | Cmd + R         | Insert a redirect node in the selected connection. |
| Open Documentation    | F1               | F1              | Open the documentation for the selected node. |
| Save                  | Ctrl + S         | Cmd + S         | Save your graph. |
| Save As...            | Ctrl + Shift + S | Cmd + Shift + S | Save your graph with a new name. |
| Toggle Blackboard     | Shift + 1        | Shift + 1       | Toggle the [Blackboard](Blackboard.md) on and off. | 
| Toggle Inspector      | Shift + 2        | Shift + 2       | Toggle the [Graph Inspector](Internal-Inspector.md) on and off. |
| Toggle Main Preview   | Shift + 3        | Shift + 3       | Toggle the [Main Preview](Main-Preview.md) on and off. |
| Toggle Node Collapsed | Ctrl + P         | Cmd + P         | Toggle the selected node's collapsed state. |
| Toggle Node Previews  | Ctrl + T         | Cmd + T         | Toggle the selected node's preview on and off. |
| Ungroup Nodes         | Ctrl + U         | Cmd + U         | Ungroup the selected nodes. |

## Node creation keyboard shortcuts

The following table lists the keyboard shortcuts you can use to add nodes in Shader Graph. You can also customize these keyboard shortcuts with the [Shortcuts Manager](https://docs.unity3d.com/Manual/ShortcutsManager.html).

| Node              | Windows   | macOS       |
|:------------------|:----------|:------------|
| Absolute          | Alt + \\  | Option + \\ |   
| Add               | Alt + A   | Option + A  | 
| Blend             | Alt + B   | Option + B  | 
| Boolean           | Alt + 0   | Option + 0  | 
| Branch            | Alt + Y   | Option + Y  | 
| Ceiling           | Alt + ]   | Option + ]  |
| Clamp             | Alt + =   | Option + =  |
| Color             | Alt + C   | Option + C  |
| Combine           | Alt + K   | Option + K  |
| Cross Product     | Alt + H   | Option + H  |
| Custom Function   | Alt + ;   | Option + ;  |
| Divide            | Alt + D   | Option + D  |
| Dot Product       | Alt + .   | Option + .  |
| Float             | Alt + 1   | Option + 1  |
| Floor             | Alt + [   | Option + [  |
| Fraction          | Alt + /   | Option + /  |
| Fresnel Effect    | Alt + F   | Option + F  |
| Gradient          | Alt + G   | Option + G  |
| Lerp              | Alt + L   | Option + L  |
| Multiply          | Alt + M   | Option + M  |
| Negate            | Alt + -   | Option + -  |
| Normal Vector     | Alt + N   | Option + N  |
| Normalize         | Alt + Z   | Option + Z  |
| One Minus         | Alt + \|  | Option + \| |
| Position          | Alt + V   | Option + V  |
| Power             | Alt + P   | Option + P  |
| Remap             | Alt + R   | Option + R  |
| Sample Texture 2D | Alt + X   | Option + X  |
| Saturate          | Alt + Q   | Option + Q  |
| Smoothstep        | Alt + "   | Option + "  |
| Split             | Alt + E   | Option + E  |
| Step              | Alt + J   | Option + J  |
| Subtract          | Alt + S   | Option + S  |
| Swizzle           | Alt + W   | Option + W  |
| Tiling and Offset | Alt + O   | Option + O  |
| Time              | Alt + T   | Option + T  |
| UV                | Alt + U   | Option + U  |
| Vector 2          | Alt + 2   | Option + 2  |
| Vector 3          | Alt + 3   | Option + 3  |
| Vector 4          | Alt + 4   | Option + 4  |

## Additional resources

- [Blackboard](Blackboard.md)
- [Create Node menu](Create-Node-Menu.md)
- [Graph Inspector](Internal-Inspector.md)
- [Main Preview](Main-Preview.md)
- [Shader Graph window](Shader-Graph-Window.md)
- [Shortcuts Manager](https://docs.unity3d.com/Manual/ShortcutsManager.html)
