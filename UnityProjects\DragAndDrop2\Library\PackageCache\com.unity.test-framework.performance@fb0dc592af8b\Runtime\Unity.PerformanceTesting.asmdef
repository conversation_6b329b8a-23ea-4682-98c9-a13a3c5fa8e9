{"name": "Unity.PerformanceTesting", "rootNamespace": "", "references": ["UnityEngine.TestRunner", "com.unity.test.metadata-manager"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_TESTS_FRAMEWORK"], "versionDefines": [{"name": "com.unity.test-framework", "expression": "", "define": "UNITY_TESTS_FRAMEWORK"}], "noEngineReferences": false}