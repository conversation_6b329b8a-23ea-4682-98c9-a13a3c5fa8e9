Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.38f1 (82314a941f2d) revision 8532298'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.38f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Desktop/UnityProjects/DragAndDrop2
-logFile
Logs/AssetImportWorker2.log
-srvPort
60147
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Desktop/UnityProjects/DragAndDrop2
C:/Users/<USER>/Desktop/UnityProjects/DragAndDrop2
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21168]  Target information:

Player connection [21168]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1401737741 [EditorId] 1401737741 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-Q6QVUO3) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21168] Host joined multi-casting on [***********:54997]...
Player connection [21168] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 5.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.38f1 (82314a941f2d)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/UnityProjects/DragAndDrop2/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a8)
    Vendor:   Intel
    VRAM:     12130 MB
    Driver:   32.0.101.6078
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56208
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.38f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008955 seconds.
- Loaded All Assemblies, in  0.866 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.846 seconds
Domain Reload Profiling: 1709ms
	BeginReloadAssembly (307ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (86ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (135ms)
	LoadAllAssembliesAndSetupDomain (308ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (302ms)
			TypeCache.Refresh (299ms)
				TypeCache.ScanAssembly (272ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (847ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (735ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (136ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.722 seconds
Refreshing native plugins compatible for Editor in 1.67 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.263 seconds
Domain Reload Profiling: 2975ms
	BeginReloadAssembly (401ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (1141ms)
		LoadAssemblies (822ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (542ms)
			TypeCache.Refresh (365ms)
				TypeCache.ScanAssembly (331ms)
			BuildScriptInfoCaches (147ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1264ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1041ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (610ms)
			ProcessInitializeOnLoadMethodAttributes (231ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 206 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (7.6 MB). Loaded Objects now: 7220.
Memory consumption went from 172.1 MB to 164.5 MB.
Total: 18.378300 ms (FindLiveObjects: 1.142300 ms CreateObjectMapping: 1.148800 ms MarkObjects: 10.152900 ms  DeleteObjects: 5.932200 ms)

========================================================================
Received Import Request.
  Time since last request: 1162088.820742 seconds.
  path: Assets/Icons
  artifactKey: Guid(d8c3032ed5f03964391eb8e0401d95ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Icons using Guid(d8c3032ed5f03964391eb8e0401d95ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7504f77e5a171ba76a95cc07249ea5e6') in 0.0071151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 136.743700 seconds.
  path: Assets/_Heathen Engineering/Assets/UX/Icons/Flat Icons [Free]/Free Flat Arrow 3 N Icon.png
  artifactKey: Guid(301fe569da9047c4988d46c1707ea4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Heathen Engineering/Assets/UX/Icons/Flat Icons [Free]/Free Flat Arrow 3 N Icon.png using Guid(301fe569da9047c4988d46c1707ea4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5b3426170b2e8f8172c07537716e3f4') in 0.2080441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 32.496636 seconds.
  path: ProjectSettings/TagManager.asset
  artifactKey: Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing ProjectSettings/TagManager.asset using Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77f9dd6f28fd7527787506f1412fa193') in 0.0016052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6571 unused Assets / (8.0 MB). Loaded Objects now: 7230.
Memory consumption went from 151.6 MB to 143.6 MB.
Total: 20.290200 ms (FindLiveObjects: 1.322700 ms CreateObjectMapping: 1.172500 ms MarkObjects: 11.150500 ms  DeleteObjects: 6.642900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.225 seconds
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.163 seconds
Domain Reload Profiling: 2386ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (790ms)
		LoadAssemblies (619ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (346ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (306ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1164ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (924ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (647ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 4.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6581 unused Assets / (7.8 MB). Loaded Objects now: 7232.
Memory consumption went from 154.6 MB to 146.7 MB.
Total: 23.281200 ms (FindLiveObjects: 1.505200 ms CreateObjectMapping: 1.495900 ms MarkObjects: 11.785200 ms  DeleteObjects: 8.486000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.282 seconds
Refreshing native plugins compatible for Editor in 4.54 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.712 seconds
Domain Reload Profiling: 2995ms
	BeginReloadAssembly (289ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (897ms)
		LoadAssemblies (662ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (370ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (325ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1713ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1250ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (257ms)
			ProcessInitializeOnLoadAttributes (809ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 4.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6581 unused Assets / (8.3 MB). Loaded Objects now: 7234.
Memory consumption went from 154.7 MB to 146.4 MB.
Total: 22.277700 ms (FindLiveObjects: 1.365100 ms CreateObjectMapping: 1.763000 ms MarkObjects: 10.487300 ms  DeleteObjects: 8.660100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 356.424262 seconds.
  path: Assets/Icons/Free Flat Arrow 3 N Icon.png
  artifactKey: Guid(301fe569da9047c4988d46c1707ea4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Icons/Free Flat Arrow 3 N Icon.png using Guid(301fe569da9047c4988d46c1707ea4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea04400b800cc873241db8de0c561bf4') in 0.2212895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6570 unused Assets / (7.8 MB). Loaded Objects now: 7235.
Memory consumption went from 155.0 MB to 147.2 MB.
Total: 14.979200 ms (FindLiveObjects: 0.975300 ms CreateObjectMapping: 0.758600 ms MarkObjects: 7.557200 ms  DeleteObjects: 5.686000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.360 seconds
Refreshing native plugins compatible for Editor in 3.02 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.186 seconds
Domain Reload Profiling: 2543ms
	BeginReloadAssembly (369ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (868ms)
		LoadAssemblies (763ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (263ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1187ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (914ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (246ms)
			ProcessInitializeOnLoadAttributes (537ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 4.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6581 unused Assets / (7.8 MB). Loaded Objects now: 7236.
Memory consumption went from 154.7 MB to 146.9 MB.
Total: 20.116500 ms (FindLiveObjects: 1.233300 ms CreateObjectMapping: 1.016300 ms MarkObjects: 11.450200 ms  DeleteObjects: 6.413800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6571 unused Assets / (7.9 MB). Loaded Objects now: 7237.
Memory consumption went from 154.9 MB to 147.0 MB.
Total: 20.822900 ms (FindLiveObjects: 1.312700 ms CreateObjectMapping: 1.399400 ms MarkObjects: 10.538800 ms  DeleteObjects: 7.569300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.483 seconds
Refreshing native plugins compatible for Editor in 2.16 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.295 seconds
Domain Reload Profiling: 2771ms
	BeginReloadAssembly (390ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (959ms)
		LoadAssemblies (787ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (323ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (274ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1297ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1009ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (606ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 3.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (7.5 MB). Loaded Objects now: 7239.
Memory consumption went from 154.7 MB to 147.2 MB.
Total: 19.895200 ms (FindLiveObjects: 1.851000 ms CreateObjectMapping: 1.229200 ms MarkObjects: 9.505300 ms  DeleteObjects: 7.307100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 134.241368 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0054054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.656066 seconds.
  path: Assets/itemSlotScript.cs
  artifactKey: Guid(5a1f04769c6cbbd40b1d65eccd627546) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/itemSlotScript.cs using Guid(5a1f04769c6cbbd40b1d65eccd627546) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57b8fbdc2893fe03805b79fc7b1c0d21') in 0.0012846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6571 unused Assets / (7.5 MB). Loaded Objects now: 7239.
Memory consumption went from 154.9 MB to 147.3 MB.
Total: 15.054800 ms (FindLiveObjects: 1.191700 ms CreateObjectMapping: 0.985500 ms MarkObjects: 7.057900 ms  DeleteObjects: 5.816400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 49.735782 seconds.
  path: Assets/itemSlotScript.cs
  artifactKey: Guid(5a1f04769c6cbbd40b1d65eccd627546) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/itemSlotScript.cs using Guid(5a1f04769c6cbbd40b1d65eccd627546) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '81c5ad28e0be5ebf55984269435a584e') in 0.001453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.298 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.058 seconds
Domain Reload Profiling: 2352ms
	BeginReloadAssembly (319ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (874ms)
		LoadAssemblies (753ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1058ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (815ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (530ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (7.2 MB). Loaded Objects now: 7241.
Memory consumption went from 154.6 MB to 147.4 MB.
Total: 16.361800 ms (FindLiveObjects: 0.956800 ms CreateObjectMapping: 0.567700 ms MarkObjects: 8.479100 ms  DeleteObjects: 6.356200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.371 seconds
Refreshing native plugins compatible for Editor in 5.03 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.679 seconds
Domain Reload Profiling: 3049ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (933ms)
		LoadAssemblies (660ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (449ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (401ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1680ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1223ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (254ms)
			ProcessInitializeOnLoadAttributes (781ms)
			ProcessInitializeOnLoadMethodAttributes (154ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.0 MB). Loaded Objects now: 7243.
Memory consumption went from 154.7 MB to 146.8 MB.
Total: 19.581400 ms (FindLiveObjects: 1.429700 ms CreateObjectMapping: 1.733000 ms MarkObjects: 8.707500 ms  DeleteObjects: 7.708100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.269 seconds
Refreshing native plugins compatible for Editor in 3.59 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.706 seconds
Domain Reload Profiling: 2975ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (830ms)
		LoadAssemblies (581ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (406ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1707ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1240ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (796ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 4.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.0 MB). Loaded Objects now: 7245.
Memory consumption went from 154.7 MB to 146.7 MB.
Total: 22.004600 ms (FindLiveObjects: 1.775100 ms CreateObjectMapping: 1.355300 ms MarkObjects: 10.182700 ms  DeleteObjects: 8.687400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.486 seconds
Refreshing native plugins compatible for Editor in 4.18 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.705 seconds
Domain Reload Profiling: 3187ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1002ms)
		LoadAssemblies (713ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (456ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (410ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1707ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1237ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (798ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (14ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 4.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.0 MB). Loaded Objects now: 7247.
Memory consumption went from 154.7 MB to 146.7 MB.
Total: 22.418800 ms (FindLiveObjects: 2.989000 ms CreateObjectMapping: 1.301500 ms MarkObjects: 11.458400 ms  DeleteObjects: 6.667700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 208.074751 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(3f9215ea0144899419cfbc0957140d3f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/DefaultVolumeProfile.asset using Guid(3f9215ea0144899419cfbc0957140d3f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c53426044daca369947a3c2f097f652e') in 0.3538471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Settings/Renderer2D.asset
  artifactKey: Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/Renderer2D.asset using Guid(424799608f7334c24bf367e4bbfa7f9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0db854cb22b200418468c569ee22d78') in 0.1103503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/SampleScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '795b4eb03cd110a6fe25fe1e246e5cf3') in 0.0066287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Settings/Scenes/URP2DSceneTemplate.unity
  artifactKey: Guid(2cda990e2423bbf4892e6590ba056729) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/Scenes/URP2DSceneTemplate.unity using Guid(2cda990e2423bbf4892e6590ba056729) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5d29cd1ff4602d17b1d80afd8e0433b') in 0.0064046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d4792bdf3904f06dd8be2dce4b78bbf') in 0.0431846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Settings/UniversalRP.asset
  artifactKey: Guid(681886c5eb7344803b6206f758bf0b1c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/UniversalRP.asset using Guid(681886c5eb7344803b6206f758bf0b1c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0633865aac239445bb6d67fb5eee71c5') in 0.0861504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/itemScript.cs
  artifactKey: Guid(60ab7bc16ac82514d871638fbc0921be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/itemScript.cs using Guid(60ab7bc16ac82514d871638fbc0921be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2df3bcf4b9f06027c065828c24388b21') in 0.0146546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Icons/Free Flat Arrow 3 N Icon.png
  artifactKey: Guid(301fe569da9047c4988d46c1707ea4e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Icons/Free Flat Arrow 3 N Icon.png using Guid(301fe569da9047c4988d46c1707ea4e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c239a7cbac0e0ac1609cf7d50350645f') in 0.076203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.279 seconds
Refreshing native plugins compatible for Editor in 4.01 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.725 seconds
Domain Reload Profiling: 3005ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (838ms)
		LoadAssemblies (568ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (408ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (367ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1726ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1261ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (253ms)
			ProcessInitializeOnLoadAttributes (818ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (14ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 4.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6584 unused Assets / (7.7 MB). Loaded Objects now: 7249.
Memory consumption went from 154.8 MB to 147.0 MB.
Total: 20.547500 ms (FindLiveObjects: 1.664200 ms CreateObjectMapping: 1.179100 ms MarkObjects: 9.975200 ms  DeleteObjects: 7.726700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.525 seconds
Refreshing native plugins compatible for Editor in 3.06 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.628 seconds
Domain Reload Profiling: 3155ms
	BeginReloadAssembly (361ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (1063ms)
		LoadAssemblies (791ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (464ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (416ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1629ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1187ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (254ms)
			ProcessInitializeOnLoadAttributes (810ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (7.5 MB). Loaded Objects now: 7251.
Memory consumption went from 154.7 MB to 147.2 MB.
Total: 13.311500 ms (FindLiveObjects: 0.877600 ms CreateObjectMapping: 0.688200 ms MarkObjects: 7.051700 ms  DeleteObjects: 4.692300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.484 seconds
Refreshing native plugins compatible for Editor in 3.66 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.676 seconds
Domain Reload Profiling: 3160ms
	BeginReloadAssembly (355ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (1029ms)
		LoadAssemblies (731ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (466ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (415ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1677ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1225ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (258ms)
			ProcessInitializeOnLoadAttributes (786ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 4.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.2 MB). Loaded Objects now: 7253.
Memory consumption went from 154.8 MB to 146.6 MB.
Total: 19.491200 ms (FindLiveObjects: 1.545200 ms CreateObjectMapping: 1.142300 ms MarkObjects: 8.578700 ms  DeleteObjects: 8.222400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6571 unused Assets / (7.5 MB). Loaded Objects now: 7253.
Memory consumption went from 154.9 MB to 147.4 MB.
Total: 19.915800 ms (FindLiveObjects: 1.372100 ms CreateObjectMapping: 1.386200 ms MarkObjects: 10.232700 ms  DeleteObjects: 6.922800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.561 seconds
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.763 seconds
Domain Reload Profiling: 3317ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (1105ms)
		LoadAssemblies (811ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (473ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (418ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1764ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1282ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (251ms)
			ProcessInitializeOnLoadAttributes (840ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (33ms)
Refreshing native plugins compatible for Editor in 4.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.3 MB). Loaded Objects now: 7255.
Memory consumption went from 154.8 MB to 146.4 MB.
Total: 23.239000 ms (FindLiveObjects: 1.894200 ms CreateObjectMapping: 1.954800 ms MarkObjects: 9.857000 ms  DeleteObjects: 9.529500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.640 seconds
Refreshing native plugins compatible for Editor in 4.66 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.705 seconds
Domain Reload Profiling: 3336ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (79ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (1110ms)
		LoadAssemblies (797ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (474ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (423ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1706ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1245ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (248ms)
			ProcessInitializeOnLoadAttributes (811ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 3.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6582 unused Assets / (8.0 MB). Loaded Objects now: 7257.
Memory consumption went from 154.8 MB to 146.8 MB.
Total: 19.249000 ms (FindLiveObjects: 1.445700 ms CreateObjectMapping: 1.420800 ms MarkObjects: 8.798300 ms  DeleteObjects: 7.582200 ms)

Prepare: number of updated asset objects reloaded= 0
