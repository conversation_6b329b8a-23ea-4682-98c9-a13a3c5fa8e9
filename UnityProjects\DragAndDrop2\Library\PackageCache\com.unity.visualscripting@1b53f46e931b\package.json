{"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "version": "1.9.5", "unity": "2021.3", "description": "Visual scripting is a workflow that uses visual, node-based graphs to design behaviors rather than write lines of C# script.\n\nEnabling artists, designers and programmers alike, visual scripting can be used to design final logic, quickly create prototypes, iterate on gameplay and create custom nodes to help streamline collaboration.\n\nVisual scripting is compatible with third-party APIs, including most packages, assets and custom libraries.", "type": "tool", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "relatedPackages": {"com.unity.visualscripting.tests": "1.9.5"}, "_upm": {"changelog": "### Fixed\n- Fixed \"NullReferenceException\" error when returning to the State Graph from the Script Graph. [UVSB-1905](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-1905)\n- Fixed compilation error when a graph contains a reference to a method with an \"in\" parameter. [UVSB-2544](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2487)\n- Added missing truncate function to Formula node [UVSB-2526](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2525)\n- Fixed an error when creating a Script Graph asset in an empty project\n\n### Changed\n- Updated deprecated EditorAnalytics APIs to new ones"}, "upmCi": {"footprint": "3c2cf4ff5283fadee37135262d5d727f8fd0be45"}, "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git", "type": "git", "revision": "3621ef0207747a605f195118ba99d1a8bdb19433"}, "_fingerprint": "1b53f46e931bea668e53f1feb0ac9138170c9455"}