{"name": "Unity.PerformanceTesting.Editor", "rootNamespace": "", "references": ["Unity.PerformanceTesting", "UnityEngine.TestRunner", "UnityEditor.TestRunner"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_TESTS_FRAMEWORK"], "versionDefines": [{"name": "com.unity.test-framework", "expression": "", "define": "UNITY_TESTS_FRAMEWORK"}], "noEngineReferences": false}