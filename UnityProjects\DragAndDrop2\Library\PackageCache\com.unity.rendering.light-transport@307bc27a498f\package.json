{"name": "com.unity.rendering.light-transport", "description": "Unity Light Transport Library exposes reusable code for writing light transport algorithms such as raytracing or pathtracing", "version": "1.0.1", "unity": "2023.3", "unityRelease": "0b1", "displayName": "Unity Light Transport Library", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}, "keywords": ["raytracing", "pathtracing", "monte-carlo"], "_fingerprint": "307bc27a498fc9cd409bbd426c85d8dc7f140bc1"}