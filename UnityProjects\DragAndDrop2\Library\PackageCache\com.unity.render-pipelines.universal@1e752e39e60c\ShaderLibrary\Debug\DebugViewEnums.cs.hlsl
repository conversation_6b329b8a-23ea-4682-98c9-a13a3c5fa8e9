//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef DEBUGVIEWENUMS_CS_HLSL
#define DEBUGVIEWENUMS_CS_HLSL
//
// UnityEngine.Rendering.Universal.DebugFullScreenMode:  static fields
//
#define DEBUGFULLSCREENMODE_NONE (0)
#define DEBUGFULLSCREENMODE_DEPTH (1)
#define DEBUGFULLSCREENMODE_MOTION_VECTOR (2)
#define DEBUGFULLSCREENMODE_ADDITIONAL_LIGHTS_SHADOW_MAP (3)
#define DEBUGFULLSCREENMODE_MAIN_LIGHT_SHADOW_MAP (4)
#define DEBUGFULLSCREENMODE_ADDITIONAL_LIGHTS_COOKIE_ATLAS (5)
#define DEBUGFULLSCREENMODE_REFLECTION_PROBE_ATLAS (6)
#define DEBUGFULLSCREENMODE_STP (7)

//
// UnityEngine.Rendering.Universal.DebugLightingFeatureFlags:  static fields
//
#define DEBUGLIGHTINGFEATUREFLAGS_NONE (0)
#define DEBUGLIGHTINGFEATUREFLAGS_GLOBAL_ILLUMINATION (1)
#define DEBUGLIGHTINGFEATUREFLAGS_MAIN_LIGHT (2)
#define DEBUGLIGHTINGFEATUREFLAGS_ADDITIONAL_LIGHTS (4)
#define DEBUGLIGHTINGFEATUREFLAGS_VERTEX_LIGHTING (8)
#define DEBUGLIGHTINGFEATUREFLAGS_EMISSION (16)
#define DEBUGLIGHTINGFEATUREFLAGS_AMBIENT_OCCLUSION (32)

//
// UnityEngine.Rendering.Universal.DebugLightingMode:  static fields
//
#define DEBUGLIGHTINGMODE_NONE (0)
#define DEBUGLIGHTINGMODE_SHADOW_CASCADES (1)
#define DEBUGLIGHTINGMODE_LIGHTING_WITHOUT_NORMAL_MAPS (2)
#define DEBUGLIGHTINGMODE_LIGHTING_WITH_NORMAL_MAPS (3)
#define DEBUGLIGHTINGMODE_REFLECTIONS (4)
#define DEBUGLIGHTINGMODE_REFLECTIONS_WITH_SMOOTHNESS (5)
#define DEBUGLIGHTINGMODE_GLOBAL_ILLUMINATION (6)

//
// UnityEngine.Rendering.Universal.DebugMaterialMode:  static fields
//
#define DEBUGMATERIALMODE_NONE (0)
#define DEBUGMATERIALMODE_ALBEDO (1)
#define DEBUGMATERIALMODE_SPECULAR (2)
#define DEBUGMATERIALMODE_ALPHA (3)
#define DEBUGMATERIALMODE_SMOOTHNESS (4)
#define DEBUGMATERIALMODE_AMBIENT_OCCLUSION (5)
#define DEBUGMATERIALMODE_EMISSION (6)
#define DEBUGMATERIALMODE_NORMAL_WORLD_SPACE (7)
#define DEBUGMATERIALMODE_NORMAL_TANGENT_SPACE (8)
#define DEBUGMATERIALMODE_LIGHTING_COMPLEXITY (9)
#define DEBUGMATERIALMODE_METALLIC (10)
#define DEBUGMATERIALMODE_SPRITE_MASK (11)
#define DEBUGMATERIALMODE_RENDERING_LAYER_MASKS (12)

//
// UnityEngine.Rendering.Universal.DebugMaterialValidationMode:  static fields
//
#define DEBUGMATERIALVALIDATIONMODE_NONE (0)
#define DEBUGMATERIALVALIDATIONMODE_ALBEDO (1)
#define DEBUGMATERIALVALIDATIONMODE_METALLIC (2)

//
// UnityEngine.Rendering.Universal.DebugMipInfoMode:  static fields
//
#define DEBUGMIPINFOMODE_NONE (0)
#define DEBUGMIPINFOMODE_MIP_STREAMING_PERFORMANCE (1)
#define DEBUGMIPINFOMODE_MIP_STREAMING_STATUS (2)
#define DEBUGMIPINFOMODE_MIP_STREAMING_ACTIVITY (3)
#define DEBUGMIPINFOMODE_MIP_STREAMING_PRIORITY (4)
#define DEBUGMIPINFOMODE_MIP_COUNT (5)
#define DEBUGMIPINFOMODE_MIP_RATIO (6)

//
// UnityEngine.Rendering.Universal.DebugMipMapModeTerrainTexture:  static fields
//
#define DEBUGMIPMAPMODETERRAINTEXTURE_CONTROL (0)
#define DEBUGMIPMAPMODETERRAINTEXTURE_LAYER0 (1)
#define DEBUGMIPMAPMODETERRAINTEXTURE_LAYER1 (2)
#define DEBUGMIPMAPMODETERRAINTEXTURE_LAYER2 (3)
#define DEBUGMIPMAPMODETERRAINTEXTURE_LAYER3 (4)

//
// UnityEngine.Rendering.Universal.DebugMipMapStatusMode:  static fields
//
#define DEBUGMIPMAPSTATUSMODE_MATERIAL (0)
#define DEBUGMIPMAPSTATUSMODE_TEXTURE (1)

//
// UnityEngine.Rendering.Universal.DebugPostProcessingMode:  static fields
//
#define DEBUGPOSTPROCESSINGMODE_DISABLED (0)
#define DEBUGPOSTPROCESSINGMODE_AUTO (1)
#define DEBUGPOSTPROCESSINGMODE_ENABLED (2)

//
// UnityEngine.Rendering.Universal.DebugSceneOverrideMode:  static fields
//
#define DEBUGSCENEOVERRIDEMODE_NONE (0)
#define DEBUGSCENEOVERRIDEMODE_OVERDRAW (1)
#define DEBUGSCENEOVERRIDEMODE_WIREFRAME (2)
#define DEBUGSCENEOVERRIDEMODE_SOLID_WIREFRAME (3)
#define DEBUGSCENEOVERRIDEMODE_SHADED_WIREFRAME (4)

//
// UnityEngine.Rendering.Universal.DebugValidationMode:  static fields
//
#define DEBUGVALIDATIONMODE_NONE (0)
#define DEBUGVALIDATIONMODE_HIGHLIGHT_NAN_INF_NEGATIVE (1)
#define DEBUGVALIDATIONMODE_HIGHLIGHT_OUTSIDE_OF_RANGE (2)

//
// UnityEngine.Rendering.Universal.DebugVertexAttributeMode:  static fields
//
#define DEBUGVERTEXATTRIBUTEMODE_NONE (0)
#define DEBUGVERTEXATTRIBUTEMODE_TEXCOORD0 (1)
#define DEBUGVERTEXATTRIBUTEMODE_TEXCOORD1 (2)
#define DEBUGVERTEXATTRIBUTEMODE_TEXCOORD2 (3)
#define DEBUGVERTEXATTRIBUTEMODE_TEXCOORD3 (4)
#define DEBUGVERTEXATTRIBUTEMODE_COLOR (5)
#define DEBUGVERTEXATTRIBUTEMODE_TANGENT (6)
#define DEBUGVERTEXATTRIBUTEMODE_NORMAL (7)

//
// UnityEngine.Rendering.Universal.HDRDebugMode:  static fields
//
#define HDRDEBUGMODE_NONE (0)
#define HDRDEBUGMODE_GAMUT_VIEW (1)
#define HDRDEBUGMODE_GAMUT_CLIP (2)
#define HDRDEBUGMODE_VALUES_ABOVE_PAPER_WHITE (3)

//
// UnityEngine.Rendering.Universal.PixelValidationChannels:  static fields
//
#define PIXELVALIDATIONCHANNELS_RGB (0)
#define PIXELVALIDATIONCHANNELS_R (1)
#define PIXELVALIDATIONCHANNELS_G (2)
#define PIXELVALIDATIONCHANNELS_B (3)
#define PIXELVALIDATIONCHANNELS_A (4)


#endif
