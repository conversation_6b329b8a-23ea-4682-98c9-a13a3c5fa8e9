%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-219497930763289538
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 13
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 32
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: HDRPLitEmissiveDetail
  m_Shader: {fileID: -6465566751694194690, guid: 7043340296acc7a43b9d5f7b8e5c4a21,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _DEPTHOFFSET_ON
  - _DETAIL_MAP
  - _DISABLE_SSR_TRANSPARENT
  - _EMISSIVE_COLOR_MAP
  - _MASKMAP
  - _NORMALMAP
  - _NORMALMAP_TANGENT_SPACE
  m_InvalidKeywords:
  - _DISPLACEMENT_LOCK_TILING_SCALE
  - _EMISSIVE_MAPPING_BASE
  - _HEIGHTMAP
  - _PIXEL_DISPLACEMENT
  - _PIXEL_DISPLACEMENT_LOCK_OBJECT_SCALE
  - _SPECULAR_OCCLUSION_NONE
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2225
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColorMap:
        m_Texture: {fileID: 2800000, guid: b356831ef4f363d48989d117c6ea79e3, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 2800000, guid: 0550da386c6a5904aa1415e1800d8c11, type: 3}
        m_Scale: {x: 12, y: 6}
        m_Offset: {x: 0, y: 0}
    - _EmissiveColorMap:
        m_Texture: {fileID: 2800000, guid: 30aa7858588e65c46a862620e7246016, type: 3}
        m_Scale: {x: 3, y: 2}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 2800000, guid: e6ba72d56495baf4a9fd0fc9c467ee6e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 2800000, guid: 8c31c20ac2129c3428084be388816642, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 7be741f9449557c488d643ae307fe1ab, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmittanceColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AORemapMax: 1
    - _AORemapMin: 0
    - _AddPrecomputedVelocity: 0
    - _AlbedoAffectEmissive: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffShadow: 0.5
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _Alpha_Clipping: 0
    - _BlendMode: 0
    - _CoatMask: 0
    - _ConservativeDepthOffsetEnable: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DETAIL_MAP: 0
    - _DepthOffsetEnable: 1
    - _DetailAlbedoScale: 1
    - _DetailNormalScale: 1
    - _DetailSmoothnessScale: 1
    - _DisplacementLockObjectScale: 1
    - _DisplacementLockTilingScale: 1
    - _DisplacementMode: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _DstBlend2: 0
    - _EMISSIVE_COLOR_MAP: 0
    - _ENABLE_GEOMETRIC_SPECULAR_AA: 0
    - _EmissiveExposureWeight: 1
    - _EmissiveIntensity: 90000
    - _EmissiveIntensityUnit: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _ExcludeFromTUAndAA: 0
    - _HEIGHTMAP: 0
    - _HeightPoMAmplitude: 10
    - _LinkDetailsWithBase: 1
    - _MASKMAP: 0
    - _MaterialID: 1
    - _MaterialTypeMask: 2
    - _Metallic: 0
    - _MetallicRemapMax: 1
    - _MetallicRemapMin: 0
    - _NORMALMAP: 0
    - _NORMALMAP_TANGENT_SPACE: 1
    - _NormalMapSpace: 0
    - _NormalScale: 1
    - _ObjectSpaceUVMapping: 0
    - _ObjectSpaceUVMappingEmissive: 0
    - _OpaqueCullMode: 2
    - _PPDLodThreshold: 5
    - _PPDMaxSamples: 32
    - _PPDMinSamples: 1
    - _PPDPrimitiveLength: 1
    - _PPDPrimitiveWidth: 1
    - _PerPixelSorting: 0
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Smoothness: 0.5
    - _SmoothnessRemapMax: 1
    - _SmoothnessRemapMin: 0
    - _SpecularAAScreenSpaceVariance: 0.1
    - _SpecularAAThreshold: 0.2
    - _SpecularOcclusionMode: 0
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 9
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 15
    - _StencilWriteMaskMV: 41
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TexWorldScale: 1
    - _TexWorldScaleEmissive: 1
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UVBase: 0
    - _UVDetail: 0
    - _UVEmissive: 6
    - _UseEmissiveIntensity: 1
    - _UseShadowThreshold: 1
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 90000, g: 90000, b: 90000, a: 90000}
    - _EmissiveColorLDR: {r: 1, g: 1, b: 1, a: 1}
    - _InvPrimScale: {r: 1, g: 1, b: 0, a: 0}
    - _UVDetailsMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskEmissive: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
