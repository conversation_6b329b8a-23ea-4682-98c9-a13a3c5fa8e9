# Unity Test Framework learning materials

This section contains courses to help you learn Unity Test Framework through a series of applied exercises. The courses are:

* The [Unity Test Framework General Introduction](./welcome.md), which gives you an opportunity to try out some of the framework's core features through general examples. Each exercise in this course is accompanied by a sample project and corresponding solution, which you can import from the Package Manager window.

* [Testing Lost Crypt](./LostCrypt/welcome.md), which shows you how to use the framework to test an actual game project.

We strongly recommend that you complete the General Introduction before you attempt Testing Lost Crypt.