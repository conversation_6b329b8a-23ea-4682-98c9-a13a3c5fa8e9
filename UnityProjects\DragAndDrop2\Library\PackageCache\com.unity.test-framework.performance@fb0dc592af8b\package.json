{"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "version": "3.0.3", "unity": "2020.3", "description": "Package that extends Unity Test Framework package. Adds performance testing capabilities and collects configuration metadata.", "keywords": ["performance", "test"], "dependencies": {"com.unity.test-framework": "1.1.31", "com.unity.modules.jsonserialize": "1.0.0"}, "relatedPackages": {"com.unity.test-framework.performance.tests": "3.0.3"}, "_upm": {"changelog": "## Fixed \n- Fixed issue where exception in OnTestEnded callback would result in EndTest method not finalising properly\n### Changed\n- Temporarily removed \"Open Script\" from Performance Benchmark Window\n- Some clarifications in documentation were added (\"Extension\" naming changed to \"Package\", Package limitations clarified)"}, "upmCi": {"footprint": "b600f4d71a9f455fc0cc194fef259373f5289c1f"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git", "type": "git", "revision": "10b82691b2d9f4e4aa8385c8d797c7e544bac548"}, "_fingerprint": "fb0dc592af8b524f1a06c50d7caf95552f1bcd2f"}