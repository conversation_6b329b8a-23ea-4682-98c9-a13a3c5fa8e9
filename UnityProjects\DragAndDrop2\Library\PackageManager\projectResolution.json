{"context": {"projectPath": "C:/Users/<USER>/Desktop/UnityProjects/DragAndDrop2/Packages", "unityVersion": "6000.0.38f1"}, "inputs": ["C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Packages\\manifest.json", "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Packages\\packages-lock.json", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\BuiltInPackagesCombined.sha1"], "outputs": {"com.unity.collab-proxy@2.7.1": {"name": "com.unity.collab-proxy", "displayName": "Version Control", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.collab-proxy@50ac96531b63", "fingerprint": "50ac96531b6358586ccfdf8cc845939bf50d9fab", "editorCompatibility": "2021.3.0f1", "version": "2.7.1", "source": "registry", "testable": false}, "com.unity.feature.2d@2.0.1": {"name": "com.unity.feature.2d", "displayName": "2D", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.feature.2d@dd1ea8910f12", "fingerprint": "dd1ea8910f12f021c166e8d0d78de44f1390ff6b", "version": "2.0.1", "source": "builtin", "testable": false}, "com.unity.ide.rider@3.0.31": {"name": "com.unity.ide.rider", "displayName": "JetBrains Rider Editor", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.ide.rider@7921be93db40", "fingerprint": "7921be93db40ec070fcb01ed82d1c3df1bbdddcd", "editorCompatibility": "2019.2.6f1", "version": "3.0.31", "source": "registry", "testable": false}, "com.unity.ide.visualstudio@2.0.22": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.ide.visualstudio@8140e851d83e", "fingerprint": "8140e851d83e922ca2021b04a89519de94ebe38c", "editorCompatibility": "2019.4.25f1", "version": "2.0.22", "source": "registry", "testable": false}, "com.unity.inputsystem@1.13.0": {"name": "com.unity.inputsystem", "displayName": "Input System", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.inputsystem@ea5ab5b33a26", "fingerprint": "ea5ab5b33a268cd12592866b410522e9f55da7b6", "editorCompatibility": "2021.3.0a1", "version": "1.13.0", "source": "registry", "testable": false}, "com.unity.multiplayer.center@1.0.0": {"name": "com.unity.multiplayer.center", "displayName": "Multiplayer Center", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.multiplayer.center@f502d8ac613f", "fingerprint": "f502d8ac613fa076192423e73892fbd89eb4049b", "editorCompatibility": "6000.0.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal@17.0.3": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal RP", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.render-pipelines.universal@1e752e39e60c", "fingerprint": "1e752e39e60cbe5784c8648daa8d82258c2e63f6", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.test-framework@1.4.6": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.test-framework@5ac417e07314", "fingerprint": "5ac417e07314c8f6afba8109738c32b82d391e68", "editorCompatibility": "2019.4.1f1", "version": "1.4.6", "source": "registry", "testable": false}, "com.unity.timeline@1.8.7": {"name": "com.unity.timeline", "displayName": "Timeline", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.timeline@c58b4ee65782", "fingerprint": "c58b4ee65782ad38338e29f7ee67787cb6998f04", "editorCompatibility": "2019.3.0a1", "version": "1.8.7", "source": "registry", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.ugui@03407c6d8751", "fingerprint": "03407c6d875144b0a8ffdc0af3e0d5cccfa5a4ae", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.visualscripting@1.9.5": {"name": "com.unity.visualscripting", "displayName": "Visual Scripting", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b", "fingerprint": "1b53f46e931bea668e53f1feb0ac9138170c9455", "editorCompatibility": "2021.3.0a1", "version": "1.9.5", "source": "registry", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ai@1.0.0": {"name": "com.unity.modules.ai", "displayName": "AI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ai", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.cloth@1.0.0": {"name": "com.unity.modules.cloth", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.cloth", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.director@1.0.0": {"name": "com.unity.modules.director", "displayName": "Director", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.director", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.terrainphysics@1.0.0": {"name": "com.unity.modules.terrainphysics", "displayName": "Terrain Physics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.terrainphysics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.umbra@1.0.0": {"name": "com.unity.modules.umbra", "displayName": "Umbra", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.umbra", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unityanalytics@1.0.0": {"name": "com.unity.modules.unityanalytics", "displayName": "Unity Analytics", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unityanalytics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vehicles@1.0.0": {"name": "com.unity.modules.vehicles", "displayName": "Vehicles", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vehicles", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.video@1.0.0": {"name": "com.unity.modules.video", "displayName": "Video", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.video", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.vr@1.0.0": {"name": "com.unity.modules.vr", "displayName": "VR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.vr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.wind@1.0.0": {"name": "com.unity.modules.wind", "displayName": "Wind", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.wind", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.xr@1.0.0": {"name": "com.unity.modules.xr", "displayName": "XR", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.xr", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.subsystems@1.0.0": {"name": "com.unity.modules.subsystems", "displayName": "Subsystems", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.subsystems", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.38f1\\Editor\\Data\\Resources\\PackageManager\\BuiltInPackages\\com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.ext.nunit@60ef35ffd3cd", "fingerprint": "60ef35ffd3cd5e2f5c8887a4a4ca6148854cd092", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "registry", "testable": false}, "com.unity.render-pipelines.core@17.0.3": {"name": "com.unity.render-pipelines.core", "displayName": "Core RP Library", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.render-pipelines.core@38fdc9b96878", "fingerprint": "38fdc9b96878a2e6f6c44f4e2f8a9e68f7b5ce46", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.0.3": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.shadergraph@e4a21741cda9", "fingerprint": "e4a21741cda9cee38944c4ebace70da6f5144489", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal RP Config", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.render-pipelines.universal-config@fa63c96d3e1a", "fingerprint": "fa63c96d3e1af317d84d171cd6c0e0658ab159df", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.2d.animation@10.1.4": {"name": "com.unity.2d.animation", "displayName": "2D Animation", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9", "fingerprint": "494a3b4e73a9ae26677ef6e9fd6bff4ca643770a", "editorCompatibility": "2023.1.0a1", "version": "10.1.4", "source": "registry", "testable": false}, "com.unity.2d.pixel-perfect@5.0.3": {"name": "com.unity.2d.pixel-perfect", "displayName": "2D Pixel Perfect", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.pixel-perfect@e3ae982b672d", "fingerprint": "e3ae982b672dc7cca42a6303bdf53b84c69991da", "editorCompatibility": "2021.1.0a1", "version": "5.0.3", "source": "registry", "testable": false}, "com.unity.2d.psdimporter@9.0.3": {"name": "com.unity.2d.psdimporter", "displayName": "2D PSD Importer", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.psdimporter@676bae148e11", "fingerprint": "676bae148e11de9a02db5a3614b8c56e4f0f44ac", "editorCompatibility": "2023.1.0a1", "version": "9.0.3", "source": "registry", "testable": false}, "com.unity.2d.sprite@1.0.0": {"name": "com.unity.2d.sprite", "displayName": "2D Sprite", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.sprite@072d7bd355e5", "fingerprint": "072d7bd355e55ee3ded20a6a52435a4f5fded0d8", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.spriteshape@10.0.7": {"name": "com.unity.2d.spriteshape", "displayName": "2D SpriteShape", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.spriteshape@9e35352ae135", "fingerprint": "9e35352ae135f602746220e7edc09eb95bbec530", "editorCompatibility": "2023.1.0a1", "version": "10.0.7", "source": "registry", "testable": false}, "com.unity.2d.tilemap@1.0.0": {"name": "com.unity.2d.tilemap", "displayName": "2D Tilemap Editor", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.tilemap@578c7061e96d", "fingerprint": "578c7061e96d989301b81e978e7d0a10f1a48e11", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.tilemap.extras@4.1.0": {"name": "com.unity.2d.tilemap.extras", "displayName": "2D Tilemap Extras", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.tilemap.extras@13634da7dbe0", "fingerprint": "13634da7dbe06c39bac6bbe2d1a166cf91f58ad7", "editorCompatibility": "6000.0.0a1", "version": "4.1.0", "source": "registry", "testable": false}, "com.unity.2d.aseprite@1.1.8": {"name": "com.unity.2d.aseprite", "displayName": "2D Aseprite Importer", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.aseprite@1f731787b516", "fingerprint": "1f731787b516be32a29864ebe53bd4e737058c54", "editorCompatibility": "2021.3.15f1", "version": "1.1.8", "source": "registry", "testable": false}, "com.unity.searcher@4.9.2": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.searcher@90d011a70418", "fingerprint": "90d011a70418fd5b0f4b8e86df39444d2af228dd", "editorCompatibility": "2019.1.0a1", "version": "4.9.2", "source": "registry", "testable": false}, "com.unity.burst@1.8.19": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.burst@7a907cf5a459", "fingerprint": "7a907cf5a4591e4e25f4203e3be84f240058c34d", "editorCompatibility": "2020.3.0a1", "version": "1.8.19", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.rendering.light-transport@307bc27a498f", "fingerprint": "307bc27a498fc9cd409bbd426c85d8dc7f140bc1", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.2d.common@9.0.7": {"name": "com.unity.2d.common", "displayName": "2D Common", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b", "fingerprint": "bb1fc9b3d81b3bb452c6708e8c088fe4224a0369", "editorCompatibility": "2023.1.0a1", "version": "9.0.7", "source": "registry", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.test-framework.performance@3.0.3": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "C:\\Users\\<USER>\\Desktop\\UnityProjects\\DragAndDrop2\\Library\\PackageCache\\com.unity.test-framework.performance@fb0dc592af8b", "fingerprint": "fb0dc592af8b524f1a06c50d7caf95552f1bcd2f", "editorCompatibility": "2020.3.0a1", "version": "3.0.3", "source": "registry", "testable": false}}}